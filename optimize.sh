#!/bin/bash

# Simple Vidur Configuration Optimizer
set -e

CONFIG_FILE="optimizer_config.yml"

# # Basic configuration
OUTPUT_DIR="config_optimizer_output_newest"
CACHE_DIR="cache_newest"

# isoqps test configuration (uncomment to use)
# OUTPUT_DIR="config_optimizer_output_isoqps"
# CACHE_DIR="cache_isoqps"

echo "Starting Vidur configuration optimization..."

# Check if config file exists
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo "Error: Configuration file $CONFIG_FILE not found!"
    exit 1
fi

# Check if we're in the right directory
if [[ ! -f "vidur/main.py" ]]; then
    echo "Error: Not in Vidur project directory! Please run from project root."
    exit 1
fi

# Create directories
mkdir -p "$CACHE_DIR" "$OUTPUT_DIR"

# Set environment variables
export WANDB_MODE=disabled
export PYTHONUNBUFFERED=1

echo "Configuration: $CONFIG_FILE"
echo "Output directory: $OUTPUT_DIR"
echo "CPU cores: $(nproc)"

# Run optimization
python -u -m vidur.config_optimizer.config_explorer.main \
    --config-path "$CONFIG_FILE" \
    --cache-dir "$CACHE_DIR" \
    --output-dir "$OUTPUT_DIR" \
    --time-limit 300 \
    --max-iterations 15 \
    --min-search-granularity 0.5 \
    --min-qps 0.01 \
    --num-threads 108   # default = nproc - 2 = 110

echo "Optimization completed!"
echo "Results saved to: $OUTPUT_DIR"