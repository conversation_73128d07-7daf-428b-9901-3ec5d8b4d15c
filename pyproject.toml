[project]
name = "vidur"
version = "0.1.0"
description = "A LLM inference cluster simulator"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "ddsketch==3.0.1",
    "fasteners==0.17.3",
    "jupyterlab==4.2.3",
    "kaleido==0.2.1",
    "matplotlib==3.9.0",
    "numpy==1.26.4",
    "paretoset==1.2.3",
    "plotly-express==0.4.1",
    "pyyaml==6.0.1",
    "randomname==0.2.1",
    "ray==2.31.0",
    "scikit-learn==1.5.0",
    "seaborn==0.13.2",
    "snakeviz==2.2.0",
    "streamlit==1.36.0",
    "vllm>=0.7.2",
    "wandb==0.16.6",
]

[dependency-groups]
dev = [
    "autopep8==2.3.1",
    "black==24.8.0",
    "flake8==7.1.1",
    "isort==5.13.2",
]
