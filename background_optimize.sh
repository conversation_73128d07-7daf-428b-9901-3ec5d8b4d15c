#!/bin/bash

# Background Vidur Configuration Optimizer
set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OPTIMIZE_SCRIPT="$SCRIPT_DIR/optimize.sh"
LOG_DIR="$SCRIPT_DIR/logs"
PID_FILE="$SCRIPT_DIR/optimize.pid"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="$LOG_DIR/optimize_${TIMESTAMP}.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

print_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

check_running() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            return 0  # Process is running
        else
            rm -f "$PID_FILE"
            return 1  # Process not running
        fi
    fi
    return 1  # PID file doesn't exist
}

start_optimization() {
    if check_running; then
        local pid=$(cat "$PID_FILE")
        print_warning "Optimization already running with PID $pid"
        print_warning "Use 'stop' command to stop it first"
        exit 1
    fi

    # Check if optimize.sh exists
    if [[ ! -f "$OPTIMIZE_SCRIPT" ]]; then
        print_error "optimize.sh not found at $OPTIMIZE_SCRIPT"
        exit 1
    fi

    # Create log directory
    mkdir -p "$LOG_DIR"

    print_status "Starting background optimization..."
    print_status "Log file: $LOG_FILE"

    # Start optimization in background with clean output
    (
        # Set environment to disable colors in most programs
        export NO_COLOR=1
        export TERM=dumb
        bash "$OPTIMIZE_SCRIPT" 2>&1
    ) > "$LOG_FILE" &
    local pid=$!
    
    # Save PID
    echo "$pid" > "$PID_FILE"
    
    print_status "Optimization started with PID $pid"
    print_status "Monitor progress with: tail -f $LOG_FILE"
    print_status "Check status with: $0 status"
    print_status "Stop with: $0 stop"
}

stop_optimization() {
    if ! check_running; then
        print_warning "No optimization process running"
        return 0
    fi

    local pid=$(cat "$PID_FILE")
    print_status "Stopping optimization process (PID: $pid)..."
    
    # Try graceful shutdown first
    kill -TERM "$pid" 2>/dev/null || true
    sleep 5
    
    # Force kill if still running
    if kill -0 "$pid" 2>/dev/null; then
        print_warning "Process still running, force killing..."
        kill -KILL "$pid" 2>/dev/null || true
    fi
    
    rm -f "$PID_FILE"
    print_status "Optimization stopped"
}

show_status() {
    if check_running; then
        local pid=$(cat "$PID_FILE")
        print_status "Optimization is running (PID: $pid)"
        
        # Show recent log entries
        if [[ -f "$LOG_FILE" ]]; then
            echo
            echo "Recent log entries:"
            tail -10 "$LOG_FILE"
        fi
    else
        print_status "No optimization process running"
    fi
}

show_logs() {
    local lines=${1:-50}
    
    if [[ -f "$LOG_FILE" ]]; then
        echo "Showing last $lines lines of current log:"
        tail -"$lines" "$LOG_FILE"
    else
        print_warning "No current log file found"
        
        # Show available log files
        if ls "$LOG_DIR"/optimize_*.log 1> /dev/null 2>&1; then
            echo
            echo "Available log files:"
            ls -lt "$LOG_DIR"/optimize_*.log | head -5
        fi
    fi
}

follow_logs() {
    if [[ -f "$LOG_FILE" ]]; then
        print_status "Following log file: $LOG_FILE"
        print_status "Press Ctrl+C to stop following"
        tail -f "$LOG_FILE"
    else
        print_error "No current log file found"
        exit 1
    fi
}

show_help() {
    echo "Usage: $0 {start|stop|status|logs|follow|help}"
    echo
    echo "Commands:"
    echo "  start   - Start optimization in background"
    echo "  stop    - Stop running optimization"
    echo "  status  - Show current status"
    echo "  logs    - Show recent log entries (default: 50 lines)"
    echo "  follow  - Follow log file in real-time"
    echo "  help    - Show this help message"
    echo
    echo "Examples:"
    echo "  $0 start                    # Start optimization"
    echo "  $0 logs 100                # Show last 100 log lines"
    echo "  $0 follow                  # Follow logs in real-time"
}

# Main command handling
case "${1:-help}" in
    start)
        start_optimization
        ;;
    stop)
        stop_optimization
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$2"
        ;;
    follow)
        follow_logs
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo
        show_help
        exit 1
        ;;
esac