# Vidur 参数寻优完整指南

## 概述

Vidur 是一个高保真度的 LLM 推理系统仿真器，提供了强大的配置优化工具，可以在不需要实际 GPU 集群的情况下进行性能分析、容量规划和配置优化。本指南详细介绍了 Vidur 的配置优化功能和使用方法。

## 1. Vidur 核心功能

### 1.1 仿真器特性
- **高保真度仿真**: 基于真实硬件性能数据训练的机器学习模型
- **事件驱动架构**: 模拟真实的请求处理流程
- **多维度配置空间**: 支持模型、硬件、调度器、并行策略等维度的组合优化
- **SLO 驱动优化**: 基于服务级别目标进行系统性能评估

### 1.2 支持的配置维度
- **模型配置**: 不同规模的 LLM 模型（8B, 70B 等）
- **硬件配置**: GPU 类型（H100, A100）和网络拓扑
- **调度策略**: 全局调度器和副本调度器
- **并行配置**: 张量并行度（TP）和流水线并行度（PP）
- **批处理配置**: 批大小和块大小设置
- **工作负载**: 不同的 trace 文件和序列长度

## 2. 配置优化器功能

### 2.1 支持的寻优模式

#### QPS 搜索 (`search_for: qps`)
**目标**: 在固定副本数下寻找满足 SLO 的最大 QPS

```yaml
search_configs:
  - trace: mooncake_conversation
    model: Meta-Llama-3-8B
    search_for: qps
    num_replicas: 8              # 固定副本数
    qps: 4.0                     # 起始 QPS
    num_requests: 1000           # 测试请求数
```

**搜索算法**: 二分搜索，搜索范围从 0 到起始 QPS 的 2 倍

#### 副本数搜索 (`search_for: num_replicas`)
**目标**: 在固定 QPS 下寻找满足 SLO 的最少副本数

```yaml
search_configs:
  - trace: arxiv_summarization
    model: Meta-Llama-3-70B
    search_for: num_replicas
    num_replicas: 16             # 起始副本数
    qps: 1.5                     # 固定 QPS
    num_requests: 300
```

**搜索算法**: 二分搜索，搜索范围从 1 到起始副本数的 2 倍

#### 等值 QPS 搜索 (`search_for: isoqps`)
**目标**: 在不同 QPS 倍数下测试系统性能表现

```yaml
search_configs:
  - trace: mooncake_conversation
    model: Meta-Llama-3-8B
    search_for: isoqps
    num_replicas: 4              # 固定副本数
    qps: 2.0                     # 基准 QPS
    qps_multipliers: [0.5, 1.0, 1.5, 2.0, 2.5, 3.0]  # 测试倍数
    num_requests: 300
```

**应用场景**: 性能曲线分析、瓶颈识别、容量规划

### 2.2 SLO 约束配置

#### 支持的性能指标
- `prefill_e2e_time`: 预填充端到端时间
- `request_e2e_time`: 请求端到端时间
- `decode_time_execution_plus_preemption_normalized`: 标准化解码时间

#### SLO 配置示例
```yaml
slo_configs:
  - name: ttft_p99_30s          # TTFT P99 <= 30s
    type: and
    quantile: 0.99
    slos:
      - metric: prefill_e2e_time
        value: 30
        
  - name: multi_metric_slo      # 多指标 SLO
    type: and                   # 支持 and/or 逻辑
    quantile: 0.99
    slos:
      - metric: prefill_e2e_time
        value: 15
      - metric: decode_time_execution_plus_preemption_normalized
        value: 0.1
```

## 3. 配置文件结构

### 3.1 完整配置示例

```yaml
# 硬件集群配置
clusters:
  - device: h100
    network_device: h100_dgx
    gpus_per_node: 8
  - device: a100
    network_device: a100_dgx
    gpus_per_node: 8

# 全局调度器配置
global_schedulers:
  - scheduler: round_robin
  - scheduler: sticky_lop
  - scheduler: lop_uncached
  - scheduler: tolerant_sticky_lop_uncached
    tolerance_factor: 2.0
  - scheduler: ranked_sticky_lop_uncached
    top_k: 2

# 请求队列配置
request_queues:
  - name: fcfs
    provider: fcfs

# 副本调度器配置
replica_schedulers:
  - scheduler: vllm_v1
    chunk_size: 512
  - scheduler: vllm_v1
    chunk_size: 1024

# 工作负载配置
traces:
  - name: mooncake_conversation
    trace_file: "./data/processed_traces/mooncake_conversation_trace.csv"
    max_seq_len: 131072
    enable_prefix_caching: true
  - name: arxiv_summarization
    trace_file: "./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv"
    max_seq_len: 65536
    enable_prefix_caching: true

# 系统参数配置
batch_sizes: [32, 64, 128]
tp_dimensions: [1, 2, 4, 8]
pp_dimensions: [1]

# 模型配置
models:
  - name: Meta-Llama-3-8B
    identifier: meta-llama/Meta-Llama-3-8B
    exclude_tp_dims: []
  - name: Meta-Llama-3-70B
    identifier: meta-llama/Meta-Llama-3-70B
    exclude_tp_dims: [1]         # 70B 模型需要并行化
```

### 3.2 配置验证规则

#### 模型-并行度约束
- 张量并行度不能超过每节点 GPU 数
- 特定模型排除某些张量并行维度
- 单副本 QPS 搜索只允许 `round_robin` 全局调度器

#### 搜索参数约束
- QPS 搜索: 需要固定 `num_replicas`
- 副本数搜索: 需要固定 `qps`
- isoqps 搜索: 需要提供 `qps_multipliers` 列表

## 4. 运行方式

### 4.1 使用优化脚本（推荐）

```bash
# 1. 编辑配置文件
vim optimizer_config.yml

# 2. 运行优化
./optimize.sh
```

### 4.2 直接命令行运行

```bash
python -u -m vidur.config_optimizer.config_explorer.main \
    --config-path optimizer_config.yml \
    --cache-dir cache \
    --output-dir config_optimizer_output \
    --time-limit 300 \
    --max-iterations 15 \
    --min-search-granularity 0.5 \
    --min-qps 0.01 \
    --num-threads 108
```

### 4.3 关键运行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--num-threads` | `nproc - 2` | 并行线程数，建议设置为 CPU 核心数 - 4 |
| `--time-limit` | 180 | 单次仿真时间限制（分钟） |
| `--max-iterations` | 10 | 二分搜索最大迭代次数 |
| `--min-search-granularity` | 1.0 | 最小搜索粒度（%） |
| `--min-qps` | 0.1 | 最小 QPS 阈值 |

## 5. 结果分析

### 5.1 输出目录结构

```
config_optimizer_output/
├── args.json                    # 运行参数
├── config.json                  # 配置文件
└── runs/
    └── <config_hash>/           # 配置哈希 ID
        ├── r8_q4.0/            # 8副本，4.0 QPS
        │   ├── <timestamp>/
        │   │   ├── plots/      # 性能图表和 CSV
        │   │   ├── config.json # 具体配置
        │   │   └── request_metrics.csv # 请求级指标
        │   └── output.log      # 运行日志
        └── r8_q8.0/            # 8副本，8.0 QPS
            └── ...
```

### 5.2 关键结果文件

#### request_metrics.csv
包含每个请求的详细指标：
- `request_id`: 请求 ID
- `prefill_e2e_time`: 预填充端到端时间
- `decode_time_per_token`: 每 token 解码时间
- `request_e2e_time`: 请求端到端时间
- `num_prefill_tokens`: 预填充 token 数
- `num_decode_tokens`: 解码 token 数

#### plots/ 目录
- `request_e2e_time_cdf.png`: 端到端时间 CDF 图
- `prefill_e2e_time_cdf.png`: 预填充时间 CDF 图
- `decode_time_per_token_cdf.png`: 解码时间分布
- `*.csv`: 对应的原始数据

### 5.3 性能指标解读

#### 关键性能指标
- **TTFT (Time To First Token)**: 首 token 延迟，对应 `prefill_e2e_time`
- **TPOT (Time Per Output Token)**: 每输出 token 时间
- **TBT (Time Between Tokens)**: token 间隔时间
- **请求吞吐量**: 单位时间处理的请求数

#### SLO 评估
- **P50/P95/P99**: 对应百分位数的性能表现
- **违反率**: 超出 SLO 阈值的请求比例
- **平均性能**: 系统整体性能水平

## 6. 优化策略与最佳实践

### 6.1 配置选择策略

#### 硬件选择
- **H100 vs A100**: H100 提供更高的计算性能和内存带宽
- **网络拓扑**: DGX 配置提供更好的 GPU 间连接性能
- **GPU 数量**: 根据模型大小和并行度需求选择

#### 调度器选择
- **全局调度器**: 
  - `round_robin`: 简单均衡，适合同质化负载
  - `lop_uncached`: 最少未完成预填充，适合异质化负载
  - `sticky_lop`: 会话亲和性，适合前缀缓存场景
- **副本调度器**:
  - `vllm_v1`: 连续批处理，块大小影响内存效率

#### 并行策略
- **张量并行**: 适合计算密集型模型，需要高带宽连接
- **流水线并行**: 适合内存受限场景，跨节点通信
- **批大小**: 平衡吞吐量和延迟

### 6.2 性能调优建议

#### 减少仿真时间
```yaml
# 测试阶段配置
num_requests: 200-500           # 减少请求数
max_seq_len: 16384             # 使用较短序列
batch_sizes: [32, 64]          # 减少批大小选项
tp_dimensions: [1, 2]          # 减少并行度选项
```

#### 提高仿真精度
```yaml
# 生产配置
num_requests: 1000-2000        # 增加请求数
--min-search-granularity 0.1  # 提高搜索精度
--max-iterations 20            # 增加搜索迭代
```

#### 系统资源优化
```bash
# 使用 tmpfs 加速 I/O
mkdir cache config_optimizer_output
sudo mount -t tmpfs tmpfs ./cache -o size=32000m
sudo mount -t tmpfs tmpfs ./config_optimizer_output -o size=32000m

# 设置合适的并行度
--num-threads $(($(nproc) - 4))  # 预留 4 核给系统
```

### 6.3 常见问题与解决方案

#### 配置错误
- **缺少性能数据**: 确保 `data/profiling/` 目录包含对应模型的性能数据
- **内存不足**: 减少 `max_seq_len` 和 `batch_sizes`
- **并行度冲突**: 检查 `exclude_tp_dims` 配置

#### 性能问题
- **仿真速度慢**: 减少 `num_requests` 和配置空间大小
- **搜索不收敛**: 调整 `min-search-granularity` 和 `max-iterations`
- **资源利用率低**: 检查 `num-threads` 设置和系统负载

## 7. 高级用法

### 7.1 多阶段优化

```bash
# 阶段 1: 快速筛选
# 使用少量请求快速评估配置空间
num_requests: 200
--time-limit 60

# 阶段 2: 精确优化  
# 对筛选出的最优配置进行精确测试
num_requests: 1000
--time-limit 300
--min-search-granularity 0.1
```

### 7.2 批量配置测试

```yaml
# 使用 isoqps 进行全面性能分析
search_configs:
  - search_for: isoqps
    qps_multipliers: [0.1, 0.2, 0.5, 0.8, 1.0, 1.2, 1.5, 2.0, 3.0, 5.0]
    # 生成详细的性能曲线
```

### 7.3 自定义性能分析

```python
# 使用 Vidur 分析工具进行后处理
from vidur.config_optimizer.analyzer import BottleneckAnalyzer

analyzer = BottleneckAnalyzer("config_optimizer_output")
bottleneck_report = analyzer.analyze_bottlenecks()
```

## 8. 总结

Vidur 配置优化器提供了全面的 LLM 推理系统性能优化能力，通过系统化的参数搜索和 SLO 驱动的评估，能够帮助用户在无需实际硬件的情况下找到最优的系统配置。合理使用不同的搜索模式和配置参数，可以有效进行容量规划、性能调优和成本优化。

关键成功要素：
1. **明确优化目标**: 选择合适的搜索模式和 SLO 约束
2. **合理配置空间**: 平衡搜索范围和计算成本
3. **充分利用硬件**: 设置合适的并行度和系统资源
4. **迭代优化策略**: 从粗粒度到细粒度逐步优化

通过遵循本指南的建议和最佳实践，用户可以充分发挥 Vidur 的优化能力，为 LLM 推理系统找到最优配置方案。