# Vidur 系统深度分析报告：仿真机制与 Profiler 设计

> **分析目标**：深入理解 Vidur LLM 推理系统仿真器的核心机制，重点解答两个关键问题：
> 1. Vidur 是如何进行仿真的？
> 2. Vidur 是如何保存 profiler 数据的？

---

## 📑 目录

- [1. 系统概览](#1-系统概览)
- [2. 核心问题解答](#2-核心问题解答)
  - [2.1 Vidur 仿真机制解析](#21-vidur-仿真机制解析)
  - [2.2 Profiler 数据管理系统](#22-profiler-数据管理系统)
- [3. 仿真引擎技术架构](#3-仿真引擎技术架构)
  - [3.1 事件驱动仿真框架](#31-事件驱动仿真框架)
  - [3.2 分层调度器体系](#32-分层调度器体系)
  - [3.3 内存与缓存管理](#33-内存与缓存管理)
  - [3.4 性能指标收集](#34-性能指标收集)
- [5. 配置参数寻优完整指南](#5-配置参数寻优完整指南)
  - [5.1 配置优化器核心功能](#51-配置优化器核心功能)
  - [5.2 寻优模式与SLO约束](#52-寻优模式与slo约束)
  - [5.3 配置文件结构与验证](#53-配置文件结构与验证)
  - [5.4 运行方式与参数调优](#54-运行方式与参数调优)
  - [5.5 结果分析与性能解读](#55-结果分析与性能解读)
  - [5.6 优化策略与最佳实践](#56-优化策略与最佳实践)
  - [5.7 高级用法与故障排除](#57-高级用法与故障排除)

---

## 1. 系统概览

### 1.1 Vidur 简介

Vidur 是一个高保真度的 LLM 推理系统仿真器，专门设计用于：

🎯 **性能分析**：研究不同工作负载和配置下的系统性能特征  
📊 **容量规划**：为 LLM 部署找到最佳的硬件配置和调度策略  
🔬 **算法研究**：快速验证新的调度算法和系统优化方案  

### 1.2 系统架构总览

Vidur 采用三层架构设计，实现了从底层性能数据收集到上层仿真分析的完整闭环：

```mermaid
graph LR
    subgraph DataLayer ["📊 Profiling 数据层"]
        A1[硬件性能数据收集] --> A2[分布式 Profiling] --> A3[智能缓存管理] --> A4[多精度计时方法]
    end
    
    subgraph SimLayer ["⚙️ Simulation 仿真层"]
        B1[事件驱动仿真引擎]
        B2[多层调度算法]
        B3[内存管理仿真]
        B4[请求生命周期管理]
    end
    
    subgraph AnalysisLayer ["📈 Analysis 分析层"]
        C1[实时性能统计]
        C2[可视化分析工具]
        C3[实验结果追踪]
        C4[性能对比分析]
    end
    
    DataLayer -->|性能数据输入| SimLayer
    SimLayer -->|仿真结果输出| AnalysisLayer
    AnalysisLayer -->|配置反馈| DataLayer
    
    style DataLayer fill:#e1f5fe
    style SimLayer fill:#f3e5f5
    style AnalysisLayer fill:#e8f5e8
```

### 1.3 核心技术特色

**🎯 高保真度仿真**
- 精确模拟真实推理系统的调度、内存管理、通信等核心机制
- 支持多种主流调度策略（FCFS、EDF、LOP、LOR 等）
- 完整的 Pipeline 并行和 Tensor 并行支持

**⚡ 高性能 Profiling**
- 四种精度可选的计时方法（CUDA_EVENT、KINETO、RECORD_FUNCTION、PERF_COUNTER）
- 分布式并行数据收集，充分利用集群资源
- 智能三级缓存机制，避免重复测量

**📊 智能预测建模**
- 基于真实硬件数据的机器学习预测模型
- 操作级别的细粒度性能建模
- 支持多种预测算法（线性回归、随机森林等）

---

## 2. 核心问题解答

本部分直接回答报告的两个核心问题。

### 2.1 Vidur 仿真机制解析

#### 2.1.1 仿真的本质：事件驱动的离散时间仿真

**Vidur 仿真的核心原理**：
Vidur 采用离散事件仿真（Discrete Event Simulation, DES）框架，将 LLM 推理系统中的所有活动建模为时间轴上的事件序列。每个事件代表系统状态的一个变化点，通过事件的有序执行来模拟真实系统的运行过程。

```python
# 仿真的核心循环
while 仍有事件需要处理:
    event = 从优先队列取出最早的事件
    当前时间 = event.时间戳
    new_events = event.处理()  # 可能产生新的后续事件
    将新事件加入优先队列
```

**关键设计要素**：

🕐 **时间精确性**：使用浮点数时间戳，支持微秒级精度  
🎯 **确定性执行**：通过事件优先级设计保证可重现性  
⚡ **高效调度**：基于堆的优先队列，O(log n) 事件调度复杂度  

#### 2.1.2 仿真的执行流程：从请求到完成的完整生命周期

**典型仿真流程**：
```
新请求到达 → 全局负载均衡 → 副本内调度 → 批次创建 → 
Pipeline 执行 → 内存管理 → 性能预测 → 结果收集 → 请求完成
```

**详细事件链**：
```
RequestArrivalEvent (请求到达)
    ↓ 触发
GlobalScheduleEvent (全局调度：选择最优副本)
    ↓ 触发
ReplicaScheduleEvent (副本调度：创建执行批次)
    ↓ 触发
BatchStageArrivalEvent (批次进入 Pipeline 阶段)
    ↓ 触发
ReplicaStageScheduleEvent (阶段调度：开始执行)
    ↓ 触发
BatchStageEndEvent (阶段完成)
    ↓ 触发 (如果是最后阶段)
BatchEndEvent (批次完成)
    ↓ 触发
PrefillEndEvent / RequestEndEvent (请求状态更新)
```

#### 2.1.3 仿真的智能调度：三级调度器协同工作

**调度层次架构**：
```
Global Scheduler (全局调度器) - 跨副本负载均衡
└── Replica Scheduler (副本调度器) - 单副本内批次管理  
    └── Stage Scheduler (阶段调度器) - Pipeline 流水线协调
```

**各层调度职责**：

1. **Global Scheduler**：负载感知的副本选择
   - LOP (Least Outstanding Prefills)：选择待处理 token 最少的副本
   - LOR (Least Outstanding Requests)：选择待处理请求最少的副本
   - 缓存感知调度：考虑前缀缓存命中率优化分配
   - 随机、Round-Robin

2. **Replica Scheduler**：智能批次组织和内存管理
   - 连续批处理：Prefill 和 Decode 混合执行
   - 前缀缓存优化：自动检测和利用共享 prompt
   - 智能抢占：内存不足时基于优先级的请求抢占

3. **Stage Scheduler**：Pipeline 并行协调
   - FIFO 阶段调度：简单高效的先进先出策略
   - 流水线流转：批次在多个 GPU 间的协调执行

#### 2.1.4 仿真的内存建模：高保真度的 KV Cache 管理

**内存管理特色**：
- **Block-based 分配**：模拟 PagedAttention 的内存管理机制
- **引用计数共享**：支持多请求共享相同内容块
- **分层缓存架构**：GPU 内存 + 磁盘缓存的混合管理
- **智能抢占策略**：LIFO 优先级抢占 + 完整请求重启

### 2.2 Profiler 数据管理系统

#### 2.2.1 核心组件概览

```mermaid
graph TD
    A[TimerStatsStore 单例管理] --> B[CudaTimer 统一计时]
    B --> C[四种 Profiling 方法]
    C --> D[科学测量流程]
    D --> E[分布式 Ray Actor]
    E --> F[三大专业模块]
    F --> G[结构化数据输出]
    
    C1[CUDA_EVENT] --> C
    C2[KINETO] --> C  
    C3[RECORD_FUNCTION] --> C
    C4[PERF_COUNTER] --> C
    
    F1[MLP 模块] --> F
    F2[Attention 模块] --> F
    F3[Collectives 模块] --> F
```

#### 2.2.2 数据管理的整体架构：分层存储 + 语义化组织

**存储结构设计**：
```
data/profiling/
├── compute/{device}/{model}/        # 计算性能数据
│   ├── attention.csv                # attention 操作 profiling
│   ├── mlp.csv                      # MLP 操作 profiling
│   └── layernorm.csv                # 归一化操作 profiling
├── network/{network_device}/        # 网络通信数据
│   ├── all_reduce.csv               # 集合通信 profiling
│   └── send_recv.csv                # 点对点通信 profiling
└── cpu_overhead/{config}/           # CPU 开销数据
    ├── cpu_overheads.csv            # CPU 操作时间
    └── scheduling.csv               # 调度开销
```

**设计理念**：

🗂️ **语义化分层**：按硬件类型和操作类型分层组织  
📊 **参数化路径**：支持动态配置替换和模板化管理  
🔄 **版本化管理**：同一配置下的多版本数据并存  

#### 2.2.3 数据收集的科学化流程：精确性与可重现性并重

**标准化 Profiling 流程**：
```python
# Vidur 的科学测量模板
def scientific_profiling_workflow():
    # Phase 1: 预热阶段 (WARMUP_STEPS=2)
    for _ in range(WARMUP_STEPS):
        execute_operation()  # 消除冷启动和缓存影响
    synchronize_device()
    
    # Phase 2: 清空历史数据
    timer_store.clear_stats()  # 确保干净的测量环境
    
    # Phase 3: 正式测量 (ACTIVE_STEPS=5)
    for _ in range(ACTIVE_STEPS):
        with PrecisionTimer(operation_name):
            execute_operation()  # 多次测量提高统计可靠性
    synchronize_device()
    
    # Phase 4: 统计分析
    return timer_store.get_comprehensive_stats()  # min/max/mean/median/std
```

**精度保障机制**：

✅ **GPU-CPU 同步**：确保异步操作完成后再计时  
✅ **多次测量**：消除偶然误差和系统抖动  
✅ **预热排除**：避免冷启动和缓存预加载影响  
✅ **完整元数据**：记录所有影响性能的配置参数  

#### 2.2.4 分布式数据收集：大规模并行 + 智能资源管理

```mermaid
sequenceDiagram
    participant M as Main Process
    participant R as Ray Cluster
    participant A as Actor Pool
    participant G as GPU Workers
    
    M->>R: 1. 集群资源发现
    R-->>M: total_gpus, node_ips
    M->>A: 2. 创建Actor池
    A->>G: 3. GPU绑定配置
    
    loop 并行Profiling
        M->>A: 4. 分发profiling任务
        A->>G: 5. 执行测量
        G-->>A: 6. 返回结果
        A-->>M: 7. 批量收集
    end
    
    M->>M: 8. 数据聚合分析
```

**Ray 并行化架构**：
```python
# 分布式 profiling 工作流
@ray.remote(num_gpus=1)
class GPUProfilerWorker:
    def profile_operation(self, config):
        # 每个 worker 绑定特定 GPU
        set_gpu_binding(self.gpu_id)
        return execute_profiling(config)

# 主控制器
def distributed_profiling(configs):
    # 创建分布式 worker 池
    workers = [GPUProfilerWorker.remote(gpu_id=i) for i in range(num_gpus)]
    
    # 并行分发任务
    futures = []
    for config in configs:
        worker_id = len(futures) % len(workers)
        future = workers[worker_id].profile_operation.remote(config)
        futures.append(future)
    
    # 批量收集结果
    return ray.get(futures)
```

**核心优势**：

🚀 **集群利用**：充分利用多节点多 GPU 资源  
⚖️ **负载均衡**：智能任务分发避免资源闲置  
🔧 **故障恢复**：单点失败不影响整体进度  

#### 2.2.5 智能缓存机制：三级缓存 + 内容感知

**三级缓存架构**：
```
Level 1: 预测结果缓存 (prediction cache) - 最快访问
    ↓ 未命中
Level 2: 训练模型缓存 (model cache) - 中等速度  
    ↓ 未命中
Level 3: 原始数据重新训练 (raw data) - 最慢但最完整
```

**缓存键设计**：
```python
def generate_cache_key(operation_config, hardware_config, model_config):
    # 基于配置哈希生成唯一缓存键
    config_signature = {
        "operation": operation_config.to_dict(),
        "hardware": hardware_config.to_dict(),  
        "model": model_config.to_dict()
    }
    return hashlib.md5(json.dumps(config_signature, sort_keys=True).encode()).hexdigest()[:8]
```

**缓存效果**：

⚡ 预测结果缓存：毫秒级响应  
🔄 训练模型缓存：避免重复训练开销  
📈 缓存命中率：通常达到 80%+ 的命中率  

#### 2.2.6 多精度计时方法：适配不同应用场景

**四种 Profiling 方法对比**：

| 方法 | 精度等级 | GPU 同步 | 性能开销 | 适用场景 | 输出格式 |
|------|----------|---------|----------|----------|----------|
| **CUDA_EVENT** | ⭐⭐⭐⭐⭐ | 自动同步 | 极低 | 🎯 生产环境精确测量 | 单一时间值 |
| **KINETO** | ⭐⭐⭐⭐⭐ | 自动同步 | 较高 | 🔍 详细性能分析 | Chrome trace |
| **RECORD_FUNCTION** | ⭐⭐⭐ | 手动同步 | 中等 | 🐛 开发调试 | 层次化 trace |
| **PERF_COUNTER** | ⭐⭐ | 手动同步 | 最低 | 🔧 兼容性测试 | CPU 时间戳 |

**选择指南**：
- **生产环境**：CUDA_EVENT（低开销 + 高精度）
- **深度分析**：KINETO（详细的 kernel 级信息）
- **开发调试**：RECORD_FUNCTION（函数调用堆栈）
- **通用场景**：PERF_COUNTER（跨平台兼容）

---

## 3. 仿真引擎技术架构

本部分深入解析 Vidur 仿真引擎的技术实现，展示其如何在软件层面准确模拟复杂的 LLM 推理系统。

### 3.1 事件驱动仿真框架

#### 3.1.1 事件系统的设计哲学

Vidur 的事件系统是整个仿真引擎的核心，它将复杂的系统交互抽象为离散的事件序列：

```python
class BaseEvent(ABC):
    def __init__(self, time: float, event_type: EventType):
        self._time = time
        self._id = BaseEvent.generate_id()  # 自增ID保证因果性
        self._event_type = event_type
        self._priority_number = self._get_priority_number()
    
    def _get_priority_number(self):
        return (self._time, self.event_type, self._id)  # 三级优先级
```

**三级优先级系统**：
1. **时间优先**：物理时间是第一优先级，确保时序正确性
2. **事件类型**：同一时间点的不同事件有固定处理顺序
3. **事件ID**：确保完全确定性，支持实验完美重现

#### 3.1.2 事件类型体系与流转机制

**事件类型层次**：
```python
class EventType(BaseIntEnum):
    BATCH_STAGE_ARRIVAL = 1      # 批次到达pipeline阶段
    REQUEST_ARRIVAL = 2          # 新请求到达系统
    BATCH_STAGE_END = 3          # pipeline阶段执行完成
    BATCH_END = 4                # 整个批次执行完成
    PREFILL_END = 5              # Prefill阶段完成
    REQUEST_END = 6              # 请求完全结束
    GLOBAL_SCHEDULE = 7          # 全局调度决策
    REPLICA_SCHEDULE = 8         # replica内部调度
    REPLICA_STAGE_SCHEDULE = 9   # pipeline阶段调度
```

**事件处理的链式反应**：
每个事件的处理可能触发后续事件，形成复杂的因果链：

```mermaid
flowchart TD
    A[RequestArrivalEvent] --> B[更新系统状态]
    B --> C[记录性能指标]
    C --> D[产生后续事件]
    
    D --> E[GlobalScheduleEvent]
    D --> F[ReplicaScheduleEvent]
    D --> G[BatchStageArrivalEvent]
    
    E --> H[选择最优Replica]
    F --> I[创建执行批次]
    G --> J[Pipeline阶段调度]
    
    H --> K[LoadBalanceEvent]
    I --> L[BatchStageEndEvent]
    J --> M[ReplicaStageScheduleEvent]
    
    style A fill:#ffcccc
    style E fill:#ccffcc
    style F fill:#ccccff
    style G fill:#ffffcc
```

```python
def handle_event(self, global_scheduler, metrics_store):
    # 1. 更新系统状态
    self._update_system_state()
    
    # 2. 记录性能指标
    metrics_store.record_event(self)
    
    # 3. 产生后续事件
    next_events = self._generate_next_events()
    return next_events
```

#### 3.1.3 主仿真循环的精妙设计

```mermaid
graph TD
    A[开始仿真] --> B{是否达到时间限制?}
    B -->|是| Z[结束仿真]
    B -->|否| C{事件队列 OR 新请求?}
    C -->|否| Z
    C -->|是| D{对比下个事件 vs 下个请求时间}
    
    D -->|请求更早| E[生成RequestArrivalEvent]
    D -->|事件更早| F[从队列取出最高优先级事件]
    
    E --> G[加入事件队列]
    G --> B
    
    F --> H[设置当前仿真时间]
    H --> I[执行事件处理]
    I --> J[收集新产生的事件]
    J --> K[批量加入事件队列]
    K --> B
    
    style A fill:#e1f5fe
    style I fill:#fff3e0
    style Z fill:#f3e5f5
```

```python
def run(self) -> None:
    while not self._time_limit_reached and (
        self._event_queue or 
        self._request_generator.get_next_request_arrival_time() is not None
    ):
        # 智能事件调度：请求生成 vs 事件处理
        next_event_time = self._event_queue[0]._time if self._event_queue else None
        next_request_time = self._request_generator.get_next_request_arrival_time()
        
        if (next_request_time is not None and 
            (next_event_time is None or next_request_time <= next_event_time)):
            # 优先处理新到达的请求
            self._add_event(RequestArrivalEvent(
                next_request_time, self._request_generator.get_next_request()
            ))
            continue

        # 处理优先级最高的事件
        event = heapq.heappop(self._event_queue)
        self._set_time(event._time)
        
        # 链式事件处理
        new_events = event.handle_event(self._scheduler, self._cluster_metric_store)
        self._add_events(new_events)
```

### 3.2 分层调度器体系

#### 3.2.1 Global Scheduler：智能负载均衡策略

**核心调度算法对比**：

| 策略 | 核心思想 | 适用场景 | 复杂度 |
|------|----------|----------|--------|
| **Random** | 随机分配 | 基准测试 | O(1) |
| **RoundRobin** | 轮询分配 | 均匀负载 | O(1) |
| **LOR** | 最少请求数 | 请求级均衡 | O(n) |
| **LOP** | 最少待处理token | Token级均衡 | O(n) |
| **LOPUncached** | 考虑缓存的LOP | 缓存感知均衡 | O(n·m) |

**LOPUncached 的创新设计**：

```mermaid
graph TD
    A[新请求到达] --> B[遍历所有Replica]
    B --> C[计算uncached_tokens]
    C --> D[预测分配后负载]
    D --> E[计算负载不平衡度]
    E --> F{还有其他Replica?}
    F -->|是| B
    F -->|否| G[选择最小不平衡度的Replica]
    
    C --> C1[检查前缀缓存]
    C1 --> C2[actual_tokens = total - cached]
    
    D --> D1[projected_load = current + uncached]
    D1 --> D2[更新负载列表]
    
    E --> E1[imbalance = max_load - min_load]
    
    style A fill:#ffcccc
    style G fill:#ccffcc
    style C1 fill:#fff3e0
```

```python
def schedule(self):
    while self._request_queue:
        request = self._request_queue.pop(0)
        
        # 计算分配到每个replica后的负载不平衡度
        load_imbalance_scores = {}
        for replica_id in self._replica_schedulers.keys():
            # 考虑前缀缓存的实际计算量
            uncached_tokens = self._get_uncached_tokens(request, replica_id)
            projected_load = self._pending_prefills[replica_id] + uncached_tokens
            
            # 计算分配后的系统负载不平衡度
            all_loads = [self._pending_prefills[rid] for rid in self._replica_schedulers.keys()]
            all_loads[replica_id] += uncached_tokens
            load_imbalance_scores[replica_id] = max(all_loads) - min(all_loads)
        
        # 选择最小化负载不平衡的replica
        best_replica = min(load_imbalance_scores.items(), key=lambda x: x[1])[0]
        request_mapping.append((best_replica, request))
```

#### 3.2.2 Replica Scheduler：VLLM V1 连续批处理

**VLLM V1 连续批处理工作流**：

```mermaid
graph TD
    A[开始调度] --> B[检查Token预算: chunk_size]
    B --> C[Phase 1: 调度运行中请求]
    
    C --> D{遍历running队列}
    D --> E[计算所需tokens]
    E --> F{尝试内存分配}
    F -->|成功| G[加入调度列表]
    F -->|失败| H[触发抢占机制]
    H --> I[释放victim内存]
    I --> J[victim重新排队]
    J --> F
    
    G --> K[更新token预算]
    K --> L{还有running请求?}
    L -->|是| D
    L -->|否| M[Phase 2: 接受新请求]
    
    M --> N{waiting队列非空 AND token预算>0}
    N -->|否| P[创建批次]
    N -->|是| O[检查前缀缓存]
    O --> Q[计算实际需要tokens]
    Q --> R{内存分配}
    R -->|成功| S[移入running队列]
    R -->|失败| P
    S --> T[更新token预算]
    T --> N
    
    P --> U[返回批次]
    
    style C fill:#e1f5fe
    style M fill:#f3e5f5
    style H fill:#fff3e0
    style O fill:#e8f5e8
```

**VLLM V1 的核心创新**：
- **统一 Token 视图**：Prefill 和 Decode 统一按 Token 处理，简化调度逻辑
- **连续批处理**：同一批次内可包含不同阶段的请求
- **智能抢占**：基于优先级的细粒度内存管理

```python
def _get_next_batch(self, current_time):
    scheduled_requests = []
    token_budget = self._config.chunk_size
    
    # Phase 1: 优先调度运行中的请求（保证连续性）
    for request in self._running:
        if token_budget <= 0:
            break
            
        tokens_needed = self._calculate_tokens_needed(request, token_budget)
        
        # 尝试内存分配，失败则触发抢占
        if self._allocate_or_preempt(request, tokens_needed):
            scheduled_requests.append(request)
            token_budget -= tokens_needed
    
    # Phase 2: 接受等待队列中的新请求
    while self._waiting_queue and token_budget > 0:
        request = self._waiting_queue.peek()
        
        # 前缀缓存优化：检查已计算的token数量
        cached_tokens = self._check_prefix_cache(request)
        tokens_needed = min(
            request.remaining_prefill_tokens - cached_tokens,
            token_budget
        )
        
        if self._allocate_or_preempt(request, tokens_needed):
            self._waiting_queue.pop()
            self._running.append(request)
            scheduled_requests.append(request)
            token_budget -= tokens_needed
        else:
            break  # 内存不足，停止接受新请求
    
    return self._create_batch(scheduled_requests) if scheduled_requests else None
```

#### 3.2.3 Stage Scheduler：Pipeline并行协调

**简洁高效的 FIFO 设计**：
```python
class ReplicaStageScheduler:
    def __init__(self, replica_id, stage_id, is_last_stage):
        self._batch_queue = deque()  # 简单的FIFO队列
        self._is_busy = False        # 单线程执行保证
        self._is_last_stage = is_last_stage
    
    def on_schedule(self):
        if self._is_busy or not self._batch_queue:
            return None, None, None
        
        # 取出队首批次并标记为忙碌
        self._is_busy = True
        batch = self._batch_queue.popleft()
        
        # 预测执行时间并创建BatchStage实体
        execution_time = self._predictor.get_execution_time(batch, self._stage_id)
        batch_stage = BatchStage(batch.id, self._stage_id, execution_time)
        
        return batch, batch_stage, execution_time
    
    def on_stage_end(self):
        self._is_busy = False  # 释放执行权
```

### 3.3 内存与缓存管理

#### 3.3.1 KV Cache 的分层架构设计

**三层管理器协同架构**：

```mermaid
graph TB
    subgraph "应用层"
        A[用户请求] --> B[Request Processing]
    end
    
    subgraph "协调层"
        C[ReplicaKVCacheManager
        统一接口] --> D[GPU/磁盘协调]
        D --> E[智能路由决策]
    end
    
    subgraph "核心层"
        F[BaseKVCacheManager
        GPU内存管理] --> G[前缀缓存]
        G --> H[Block Pool管理]
        H --> I[引用计数]
    end
    
    subgraph "扩展层"  
        J[DiskKVCacheManager
        磁盘缓存] --> K[容量扩展]
        K --> L[持久化存储]
    end
    
    B --> C
    E --> F
    E --> J
    
    style C fill:#e1f5fe
    style F fill:#f3e5f5
    style J fill:#e8f5e8
```

**三层管理器协同工作**：
```
ReplicaKVCacheManager (协调层) - 统一接口，协调GPU+磁盘
├── BaseKVCacheManager (核心层) - GPU内存管理，前缀缓存
└── DiskKVCacheManager (扩展层) - 磁盘缓存，容量扩展
```

**核心数据结构**：
```python
class KVCacheBlock:
    def __init__(self, block_id: int):
        self.block_id = block_id
        self.ref_cnt = 0                  # 引用计数支持共享
        self.block_hash = None            # 内容哈希用于缓存
        self.prev_block = None            # 双向链表支持LRU
        self.next_block = None

class BlockPool:
    def __init__(self, num_blocks: int):
        self.blocks = [KVCacheBlock(i) for i in range(num_blocks)]
        self.free_queue = FreeBlockQueue(self.blocks)
        # 两层缓存：hash -> {block_id -> block}
        self.cache_index = defaultdict(dict)
```

#### 3.3.2 智能内存分配算法

**前缀缓存的链式检查**：
```python
def get_computed_blocks(self, request):
    block_hashes = self._get_or_compute_hashes(request)
    computed_blocks = []
    
    # 关键：链式依赖检查（一旦断链，后续必然未缓存）
    for block_hash in block_hashes:
        if cached_block := self.block_pool.get_cached_block(block_hash):
            computed_blocks.append(cached_block)
        else:
            break  # 链式依赖断裂，停止检查
    
    # 更新缓存统计
    self.stats.queries += len(block_hashes)
    self.stats.hits += len(computed_blocks)
    
    return computed_blocks, len(computed_blocks) * self.block_size
```

**多层抢占机制**：

```mermaid
graph TD
    A[内存分配请求] --> B{尝试直接分配}
    B -->|成功| C[返回blocks]
    B -->|失败| D{可以抢占?}
    D -->|否| E[分配失败]
    D -->|是| F[LIFO抢占策略]
    
    F --> G[选择队尾victim]
    G --> H[释放victim的所有blocks]
    H --> I[重置victim状态]
    I --> J[victim重新排队]
    J --> K[更新free_queue]
    K --> B
    
    style A fill:#e1f5fe
    style F fill:#fff3e0
    style E fill:#ffcdd2
    style C fill:#c8e6c9
```

```python
def allocate_with_preemption(self, request, tokens_needed):
    while True:
        # 尝试直接分配
        blocks = self._try_allocate(request, tokens_needed)
        if blocks is not None:
            return blocks
        
        # 分配失败，执行抢占
        if not self._can_preempt():
            return None  # 无法抢占，分配失败
        
        # LIFO抢占策略：移除最低优先级请求
        victim = self._running.pop()  # 队尾 = 最低优先级
        self._free_blocks(victim)     # 释放所有blocks
        victim.restart()              # 重置请求状态
        self._waiting_queue.push(victim)  # 重新排队等待
```

#### 3.3.3 前缀缓存的哈希链设计

**链式哈希计算流程**：

```mermaid
graph LR
    A[Token Block 1] --> B[Hash 1]
    B --> C[Token Block 2] 
    C --> D["Hash 2 = f(Hash1, Block2)"]
    D --> E[Token Block 3]
    E --> F["Hash 3 = f(Hash2, Block3)"]
    F --> G[...]
    
    H[前缀匹配检查] --> I{Hash 1 存在?}
    I -->|是| J{Hash 2 存在?}
    I -->|否| K[缓存断链，停止检查]
    J -->|是| L{Hash 3 存在?}
    J -->|否| K
    L -->|是| M[继续检查...]
    L -->|否| K
    
    style B fill:#e1f5fe
    style D fill:#e1f5fe  
    style F fill:#e1f5fe
    style K fill:#ffcdd2
```

**链式哈希计算**：
```python
def compute_block_hashes(request):
    hashes = []
    prev_hash = None
    
    for i, block_tokens in enumerate(request.token_blocks):
        # 链式依赖：当前block哈希依赖前一个block
        current_hash = hash_function(prev_hash, block_tokens)
        hashes.append(current_hash)
        prev_hash = current_hash.value
    
    return hashes
```

**缓存效果示例**：

```mermaid
graph TD
    subgraph "Request A"
        A1[请分析这段代码的] --> A2[性能瓶颈和优化建议]
    end
    
    subgraph "Request B"  
        B1[请分析这段代码的] --> B2[逻辑错误和修复方案]
    end
    
    subgraph "缓存状态"
        C1[Block 1: 缓存命中 ✓] 
        C2[Block 2: 缓存命中 ✓]
        C3[Block 3: 需要计算 ✗]
        C4[Block 4: 需要计算 ✗]
    end
    
    A1 -.-> C1
    A1 -.-> C2
    A2 -.-> C3
    
    B1 -.-> C1
    B1 -.-> C2  
    B2 -.-> C4
    
    style C1 fill:#c8e6c9
    style C2 fill:#c8e6c9
    style C3 fill:#ffcdd2
    style C4 fill:#ffcdd2
```

```
Request A: "请分析这段代码的性能瓶颈和优化建议"
Request B: "请分析这段代码的逻辑错误和修复方案"

共同前缀: "请分析这段代码的"
缓存命中: Block_1, Block_2 (共2个blocks)
新计算: Block_3, Block_4 (仅2个blocks)
节省比例: 50% 计算量 + 50% 内存分配
```

### 3.4 性能指标收集

#### 3.4.1 分层指标体系

**指标分类和层次**：
```python
# 集群级别 - 全局视图
ClusterMetricsStore
├── replica_0: ReplicaMetricsStore  # 副本级详细指标
├── replica_1: ReplicaMetricsStore
└── ...

# 副本级别 - 详细追踪
ReplicaMetricsStore
├── Request级指标: {e2e_time, scheduling_delay, execution_time}
├── Batch级指标: {batch_size, num_tokens, processing_time}
├── Operation级指标: {attn_time, mlp_time, comm_time}
└── Resource级指标: {memory_usage, cache_hit_rate, utilization}
```

#### 3.4.2 智能数据结构

**CDFSketch - 内存高效的分布估计**：
```python
class CDFSketch:
    def __init__(self, relative_accuracy=0.01):
        self._buckets = defaultdict(int)  # 值桶计数
        self._min_value = float('inf')
        self._max_value = float('-inf')
        self._count = 0
    
    def add(self, value):
        bucket_index = self._get_bucket_index(value)
        self._buckets[bucket_index] += 1
        self._count += 1
        self._update_extremes(value)
    
    def get_quantile(self, q):
        target_rank = int(q * self._count)
        current_rank = 0
        
        for bucket_index in sorted(self._buckets.keys()):
            current_rank += self._buckets[bucket_index]
            if current_rank >= target_rank:
                return self._bucket_to_value(bucket_index)
```

**DataSeries - 内存优化的时间序列**：
```python
class DataSeries:
    def __init__(self, max_points=10000, sampling_rate=0.1):
        self._data = []
        self._max_points = max_points
        self._sampling_rate = sampling_rate
        self._total_added = 0
    
    def add_point(self, x, y):
        self._total_added += 1
        
        if len(self._data) < self._max_points:
            self._data.append((x, y))
        else:
            # 概率采样替换，保持数据量恒定
            if random.random() < self._sampling_rate:
                idx = random.randint(0, len(self._data) - 1)
                self._data[idx] = (x, y)
```

#### 3.4.3 多格式输出支持

**输出格式适配**：
- **CSV结构化数据**：便于Excel/Python后续分析
- **WandB实验追踪**：云端实验管理和对比
- **Chrome Traces**：详细的执行时序可视化
- **实时图表**：Matplotlib/Seaborn动态图表

```python
def export_metrics(self, formats=['csv', 'wandb', 'chrome_trace']):
    results = {}
    
    if 'csv' in formats:
        results['csv'] = self._export_to_csv()
    
    if 'wandb' in formats:
        results['wandb'] = self._export_to_wandb()
    
    if 'chrome_trace' in formats:
        results['chrome_trace'] = self._export_to_chrome_trace()
    
    return results
```

## 5. 配置参数寻优完整指南

Vidur 提供了强大的配置优化工具，可以在不需要实际 GPU 集群的情况下进行性能分析、容量规划和配置优化。本章详细介绍 Vidur 的配置优化功能和使用方法。

### 5.1 配置优化器核心功能

#### 5.1.1 仿真器特性

Vidur 配置优化器具备以下核心特性：

🎯 **高保真度仿真**：基于真实硬件性能数据训练的机器学习模型  
🔄 **事件驱动架构**：模拟真实的请求处理流程  
📊 **多维度配置空间**：支持模型、硬件、调度器、并行策略等维度的组合优化  
🎚️ **SLO 驱动优化**：基于服务级别目标进行系统性能评估  

#### 5.1.2 支持的配置维度

**模型配置**：不同规模的 LLM 模型（8B, 70B 等）  
**硬件配置**：GPU 类型（H100, A100）和网络拓扑  
**调度策略**：全局调度器和副本调度器  
**并行配置**：张量并行度（TP）和流水线并行度（PP）  
**批处理配置**：批大小和块大小设置  
**工作负载**：不同的 trace 文件和序列长度  

### 5.2 寻优模式与 SLO 约束

#### 5.2.1 支持的寻优模式

**QPS 搜索 (`search_for: qps`)**

目标：在固定副本数下寻找满足 SLO 的最大 QPS

```yaml
search_configs:
  - trace: mooncake_conversation
    model: Meta-Llama-3-8B
    search_for: qps
    num_replicas: 8              # 固定副本数
    qps: 4.0                     # 起始 QPS
    num_requests: 1000           # 测试请求数
```

搜索算法：二分搜索，搜索范围从 0 到起始 QPS 的 2 倍

**副本数搜索 (`search_for: num_replicas`)**

目标：在固定 QPS 下寻找满足 SLO 的最少副本数

```yaml
search_configs:
  - trace: arxiv_summarization
    model: Meta-Llama-3-70B
    search_for: num_replicas
    num_replicas: 16             # 起始副本数
    qps: 1.5                     # 固定 QPS
    num_requests: 300
```

搜索算法：二分搜索，搜索范围从 1 到起始副本数的 2 倍

**等值 QPS 搜索 (`search_for: isoqps`)**

目标：在不同 QPS 倍数下测试系统性能表现

```yaml
search_configs:
  - trace: mooncake_conversation
    model: Meta-Llama-3-8B
    search_for: isoqps
    num_replicas: 4              # 固定副本数
    qps: 2.0                     # 基准 QPS
    qps_multipliers: [0.5, 1.0, 1.5, 2.0, 2.5, 3.0]  # 测试倍数
    num_requests: 300
```

应用场景：性能曲线分析、瓶颈识别、容量规划

#### 5.2.2 SLO 约束配置

**支持的性能指标**：
- `prefill_e2e_time`: 预填充端到端时间
- `request_e2e_time`: 请求端到端时间  
- `decode_time_execution_plus_preemption_normalized`: 标准化解码时间

**SLO 配置示例**：
```yaml
slo_configs:
  - name: ttft_p99_30s          # TTFT P99 <= 30s
    type: and
    quantile: 0.99
    slos:
      - metric: prefill_e2e_time
        value: 30
        
  - name: multi_metric_slo      # 多指标 SLO
    type: and                   # 支持 and/or 逻辑
    quantile: 0.99
    slos:
      - metric: prefill_e2e_time
        value: 15
      - metric: decode_time_execution_plus_preemption_normalized
        value: 0.1
```

### 5.3 配置文件结构与验证

#### 5.3.1 完整配置示例

```yaml
# 硬件集群配置
clusters:
  - device: h100
    network_device: h100_dgx
    gpus_per_node: 8
  - device: a100
    network_device: a100_dgx
    gpus_per_node: 8

# 全局调度器配置
global_schedulers:
  - scheduler: round_robin
  - scheduler: sticky_lop
  - scheduler: lop_uncached
  - scheduler: tolerant_sticky_lop_uncached
    tolerance_factor: 2.0
  - scheduler: ranked_sticky_lop_uncached
    top_k: 2

# 请求队列配置
request_queues:
  - name: fcfs
    provider: fcfs

# 副本调度器配置
replica_schedulers:
  - scheduler: vllm_v1
    chunk_size: 512
  - scheduler: vllm_v1
    chunk_size: 1024

# 工作负载配置
traces:
  - name: mooncake_conversation
    trace_file: "./data/processed_traces/mooncake_conversation_trace.csv"
    max_seq_len: 131072
    enable_prefix_caching: true
  - name: arxiv_summarization
    trace_file: "./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv"
    max_seq_len: 65536
    enable_prefix_caching: true

# 系统参数配置
batch_sizes: [32, 64, 128]
tp_dimensions: [1, 2, 4, 8]
pp_dimensions: [1]

# 模型配置
models:
  - name: Meta-Llama-3-8B
    identifier: meta-llama/Meta-Llama-3-8B
    exclude_tp_dims: []
  - name: Meta-Llama-3-70B
    identifier: meta-llama/Meta-Llama-3-70B
    exclude_tp_dims: [1]         # 70B 模型需要并行化
```

#### 5.3.2 配置验证规则

**模型-并行度约束**：
- 张量并行度不能超过每节点 GPU 数
- 特定模型排除某些张量并行维度
- 单副本 QPS 搜索只允许 `round_robin` 全局调度器

**搜索参数约束**：
- QPS 搜索: 需要固定 `num_replicas`
- 副本数搜索: 需要固定 `qps`
- isoqps 搜索: 需要提供 `qps_multipliers` 列表

#### 5.3.3 关键运行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--num-threads` | `nproc - 2` | 并行线程数，建议设置为 CPU 核心数 - 4 |
| `--time-limit` | 180 | 单次仿真时间限制（分钟） |
| `--max-iterations` | 10 | 二分搜索最大迭代次数 |
| `--min-search-granularity` | 1.0 | 最小搜索粒度（%） |
| `--min-qps` | 0.1 | 最小 QPS 阈值 |

### 5.4 结果分析与性能解读

#### 5.4.1 输出目录结构

```
config_optimizer_output/
├── args.json                    # 运行参数
├── config.json                  # 配置文件
└── runs/
    └── <config_hash>/           # 配置哈希 ID
        ├── r8_q4.0/            # 8副本，4.0 QPS
        │   ├── <timestamp>/
        │   │   ├── plots/      # 性能图表和 CSV
        │   │   ├── config.json # 具体配置
        │   │   └── request_metrics.csv # 请求级指标
        │   └── output.log      # 运行日志
        └── r8_q8.0/            # 8副本，8.0 QPS
            └── ...
```

#### 5.4.2 关键结果文件

**request_metrics.csv**

包含每个请求的详细指标：
- `request_id`: 请求 ID
- `prefill_e2e_time`: 预填充端到端时间
- `decode_time_per_token`: 每 token 解码时间
- `request_e2e_time`: 请求端到端时间
- `num_prefill_tokens`: 预填充 token 数
- `num_decode_tokens`: 解码 token 数

**plots/ 目录**：
- `request_e2e_time_cdf.png`: 端到端时间 CDF 图
- `prefill_e2e_time_cdf.png`: 预填充时间 CDF 图
- `decode_time_per_token_cdf.png`: 解码时间分布
- `*.csv`: 对应的原始数据

#### 5.4.3 性能指标解读

**关键性能指标**：
- **TTFT (Time To First Token)**: 首 token 延迟，对应 `prefill_e2e_time`
- **TPOT (Time Per Output Token)**: 每输出 token 时间
- **TBT (Time Between Tokens)**: token 间隔时间
- **请求吞吐量**: 单位时间处理的请求数

**SLO 评估**：
- **P50/P95/P99**: 对应百分位数的性能表现
- **违反率**: 超出 SLO 阈值的请求比例
- **平均性能**: 系统整体性能水平

### 5.5 优化策略与最佳实践

#### 5.5.1 配置选择策略

**硬件选择**：
- **H100 vs A100**: H100 提供更高的计算性能和内存带宽
- **网络拓扑**: DGX 配置提供更好的 GPU 间连接性能
- **GPU 数量**: 根据模型大小和并行度需求选择

**调度器选择**：
- **全局调度器**: 
  - `round_robin`: 简单均衡，适合同质化负载
  - `lop_uncached`: 最少未完成预填充，适合异质化负载
  - `sticky_lop`: 会话亲和性，适合前缀缓存场景
- **副本调度器**:
  - `vllm_v1`: 连续批处理，块大小影响内存效率

**并行策略**：
- **张量并行**: 适合计算密集型模型，需要高带宽连接
- **流水线并行**: 适合内存受限场景，跨节点通信
- **批大小**: 平衡吞吐量和延迟

