clusters: # Available node types
  - device: h100
    network_device: h100_dgx
    gpus_per_node: 8
  - device: a100
    network_device: a100_dgx
    gpus_per_node: 8

global_schedulers: # Routing policies to be tested, some policies require their own parameters.
  - scheduler: round_robin
  - scheduler: sticky_lop
  - scheduler: lop_uncached
  # - scheduler: tolerant_sticky_lop_uncached
  #   tolerance_factor: 2.0
  # - scheduler: tolerant_sticky_lop_uncached
  #   tolerance_factor: 3.0
  # - scheduler: ranked_sticky_lop_uncached
  #   top_k: 2
  # - scheduler: ranked_sticky_lop_uncached
  #   top_k: 4

request_queues:
  - name: fcfs
    provider: fcfs

replica_schedulers:
  - scheduler: vllm_v1
    chunk_size: 4096
  - scheduler: vllm_v1
    chunk_size: 8192

slo_configs: # Latency SLOs to be tested
  - name: ttft_p99_30s # TTFT p99 should be at max 30s (corrected name)
    type: and
    quantile: 0.99
    slos:
      - metric: prefill_e2e_time
        value: 30
  - name: ttlt_p99_60s # TTLT p99 should be at max 60s
    type: and
    quantile: 0.99
    slos:
      - metric: request_e2e_time
        value: 60
  - name: ttft_p99_15s_and_tpot_p99_100ms # 99% of requests should have TTFT <= 15s and TPOT <= 100ms
    type: and # `or` is also supported
    quantile: 0.99
    slos:
      - metric: prefill_e2e_time
        value: 15
      - metric: decode_time_execution_plus_preemption_normalized
        value: 0.1

traces:
  # - name: splitwise_conv
  #   trace_file: "./data/processed_traces/splitwise_conv.csv"
  #   max_seq_len: 16384
  #   enable_prefix_caching: false
  - name: mooncake_conversation
    trace_file: "./data/processed_traces/mooncake_conversation_trace.csv"
    max_seq_len: 131072
    enable_prefix_caching: true
  - name: arxiv_summarization
    trace_file: "./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv"
    max_seq_len: 65536
    enable_prefix_caching: true

# allowed batch sizes and TP/PP dimensions  
batch_sizes: [256, 512]  # Up to profiling limit of 512
tp_dimensions: [1, 2, 4, 8]
pp_dimensions: [1]

models:
  - name: Meta-Llama-3-8B
    identifier: meta-llama/Meta-Llama-3-8B
    exclude_tp_dims: []  # H100 supports all TP dimensions for 8B
  - name: Meta-Llama-3-70B
    identifier: meta-llama/Meta-Llama-3-70B
    exclude_tp_dims: [1]  # 70B model needs parallelism

search_configs:
  # - trace: mooncake_conversation
  #   model: Meta-Llama-3-8B
  #   search_for: qps
  #   num_replicas: 8
  #   qps: 4.0  # Increased to expand search range
  #   num_requests: 1000  # Reduced for faster testing
    # The above item searches for the maximum QPS (starting at 4.0, can go lower or higher) that can be achieved with 8 replicas of Meta-Llama-3-8B model on mooncake_conversation trace while being under the SLOs defined above.
  # - trace: arxiv_summarization
  #   model: Meta-Llama-3-8B
  #   search_for: qps
  #   num_replicas: 8
  #   qps: 5.0  # Increased for better search range
  #   num_requests: 500  # Reduced for faster testing
  # - trace: splitwise_conv
  #   model: Meta-Llama-3-70B
  #   search_for: qps
  #   num_replicas: 16
  #   qps: 1.0  # Increased from 0.2 to expand search range
  #   num_requests: 500   # Reduced for faster testing
  # - trace: arxiv_summarization
  #   model: Meta-Llama-3-70B
  #   search_for: qps
  #   num_replicas: 16
  #   qps: 1.5  # Increased from 0.3 for better search range
  #   num_requests: 300   # Reduced for faster testing

  # isoqps 搜索配置 - 测试不同QPS倍数下的性能表现
  - trace: mooncake_conversation
    model: Meta-Llama-3-8B
    search_for: isoqps
    num_replicas: 4              # 固定副本数
    qps: 2.0                     # 基准QPS
    qps_multipliers: [0.5, 1.0, 1.5, 2.0, 2.5, 3.0]  # 测试 1.0, 2.0, 3.0, 4.0, 5.0, 6.0 QPS
    num_requests: 300            # 每个QPS点的请求数
