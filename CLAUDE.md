# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Vidur is a high-fidelity LLM inference system simulator that models real-world deployment scenarios including scheduling algorithms, memory management, and hardware constraints. It enables performance analysis, capacity planning, and research on inference optimization without requiring actual GPU clusters.

## Common Development Commands

### Setup
```bash
# Create and activate virtual environment
uv venv
source .venv/bin/activate

# Install dependencies
uv sync
```

### Code Quality
```bash
# Run linting
make lint

# Format code
make format

# Individual tools
make lint/black    # Check style with black
make lint/isort    # Check style with isort  
make format/black  # Format with black
make format/isort  # Format with isort
```

### Running Simulations
```bash
# Basic simulation example
python -m vidur.main \
--time_limit 10800 \
--replica_config_model_name meta-llama/Meta-Llama-3-8B \
--replica_config_device h100 \
--replica_config_network_device h100_dgx \
--cluster_config_num_replicas 8 \
--replica_config_tensor_parallel_size 1 \
--replica_config_num_pipeline_stages 1 \
--request_generator_config_type synthetic \
--synthetic_request_generator_config_num_requests 128 \
--length_generator_config_type trace \
--trace_request_length_generator_config_trace_file ./data/processed_traces/mooncake_conversation_trace.csv \
--interval_generator_config_type poisson \
--poisson_request_interval_generator_config_qps 8.0 \
--global_scheduler_config_type round_robin \
--replica_scheduler_config_type vllm_v1 \
--vllm_v1_scheduler_config_chunk_size 512 \
--vllm_v1_scheduler_config_batch_size_cap 512 \
--cache_config_enable_prefix_caching

# Get help for all parameters
python -m vidur.main -h
```

### Output and Analysis
- Simulation results are saved to `simulator_output/<TIMESTAMP>/`
- Metrics are automatically plotted and stored as CSV files
- Chrome traces can be viewed at `chrome://tracing/` 
- Optional WandB integration for experiment tracking

## Core Architecture

### Event-Driven Simulation Framework
- **Simulator**: Central orchestrator using priority-based event queue
- **Events**: `RequestArrivalEvent`, `BatchEndEvent`, `GlobalScheduleEvent`, etc.
- **Flow**: Events trigger scheduling decisions → batch execution → completion events

### Two-Level Scheduling Hierarchy

#### Global Schedulers (`vidur/scheduler/global_scheduler/`)
Route requests across replicas:
- **LOP**: Least Outstanding Prefills (recommended)
- **Round Robin**: Simple cyclic distribution
- **LOR**: Least Outstanding Requests
- **Sticky variants**: Session affinity for prefix caching

#### Replica Schedulers (`vidur/scheduler/replica_scheduler/`)
Create batches within replicas:
- **VLLM V1**: Continuous batching with chunked prefill (recommended)
- **Sarathi**: Advanced chunked prefill scheduling
- **ORCA**: Iteration-level scheduling

### Core Entities (`vidur/entities/`)
- **Cluster**: Container for multiple replicas
- **Replica**: Individual inference node with tensor/pipeline parallelism
- **Request**: Workload unit with prefill/decode tokens, lifecycle tracking
- **Batch**: Groups requests for parallel execution

### Memory Management (`vidur/kv_cache/`)
- **Block-based allocation**: Similar to PagedAttention
- **Prefix caching**: Shared prompt optimization
- **Disk caching**: Memory overflow handling
- **Watermarks**: OOM prevention

### Execution Time Prediction (`vidur/execution_time_predictor/`)
- **ML models**: Linear regression, Random Forest
- **Profiling data**: Real hardware measurements in `data/profiling/`
- **Granular prediction**: Per-operation timing (attention, MLP, collectives)

### Request Generation (`vidur/request_generator/`)
- **Synthetic workloads**: Poisson, Gamma distributions
- **Trace-driven**: Replay real workload patterns
- **Configurable**: Token lengths, arrival intervals

### Configuration System (`vidur/config/`)
- **Hierarchical**: Simulation → Cluster → Replica → Model
- **CLI integration**: Automatic argument parsing from dataclasses
- **Model profiles**: Pre-configured LLM architectures
- **Hardware profiles**: GPU/network device specifications

## Key File Locations

### Main Entry Points
- `vidur/main.py`: CLI entry point
- `vidur/simulator.py`: Core simulation engine

### Configuration
- `vidur/config/config.py`: Main configuration dataclasses
- `vidur/config/model_config.py`: Supported model definitions
- `vidur/config/device_sku_config.py`: Hardware profiles

### Core Logic
- `vidur/scheduler/`: All scheduling algorithms
- `vidur/entities/`: Core data structures
- `vidur/events/`: Event system
- `vidur/kv_cache/`: Memory management
- `vidur/metrics/`: Performance tracking

### Documentation
- `docs/how_to_run.md`: Detailed usage guide
- `docs/metrics.md`: Available metrics description
- `docs/profiling.md`: Adding new models/hardware

## Development Patterns

### Adding New Schedulers
1. Inherit from appropriate base class (`BaseGlobalScheduler` or `BaseReplicaScheduler`)
2. Implement required abstract methods
3. Add to registry in respective `_registry.py` file
4. Add configuration dataclass extending base config

### Adding New Models
1. Add model configuration to `model_config.py`
2. Profile compute and attention operations (see `docs/profiling.md`)
3. Update execution time predictor data

### Configuration Extensions
- Use dataclass inheritance from base configs
- Implement `get_type()` method for polymorphism
- Register in appropriate registries
- Add CLI argument metadata in field definitions

### Event Handling
- Events are processed in time-order priority
- Each event's `handle_event()` returns list of new events
- Use proper event types for clear simulation flow

## Testing and Validation

Since this is a simulation framework, validation typically involves:
- Comparing outputs against known baselines
- Verifying scheduling behavior matches expectations
- Cross-checking metrics for consistency
- Performance profiling for simulation efficiency

Always run linting before committing changes to maintain code quality standards.

## Recent Analysis Sessions

### Config Optimizer Deep Dive (2025-01-28)

**User Request**: 查看 `vidur/config_optimizer` 目录结构和功能分析

**Key Findings**:

#### Directory Structure
```
vidur/config_optimizer/
├── analyzer/                    # 分析和可视化工具
│   ├── bottleneck_analyzer.py   # 瓶颈分析器
│   ├── bottleneck_cases.py      # 瓶颈场景定义
│   ├── dashboard/               # Web 仪表板
│   │   ├── main.py             # Streamlit 主界面
│   │   ├── *_page.py           # 各功能页面
│   │   └── assets/             # 静态资源
│   ├── generate_pareto_curves.py # 帕累托曲线生成
│   └── stats_extractor.py       # 统计数据提取
└── config_explorer/             # 配置探索器
    ├── main.py                  # 入口程序
    ├── config_explorer.py       # 核心调度器
    ├── capacity_search.py       # 容量搜索算法
    ├── ray_utils.py            # Ray 并行化工具
    └── config/                 # 配置定义
```

#### Core Functionality

**Config Explorer**:
- 自动搜索不同系统配置下的最优性能参数
- 支持三种搜索类型：
  - `qps`: 在给定副本数下搜索最大 QPS
  - `num_replicas`: 在给定 QPS 下搜索最少副本数
  - `isoqps`: 在不同 QPS 倍数下进行等值搜索
- 使用二分搜索算法找到满足 SLO 的最优配置点
- 基于 Ray 框架实现分布式并行搜索

**Performance Analyzer**:
- **瓶颈分析**: 自动识别系统瓶颈类型（内存限制、批处理饱和、预填充吞吐量等）
- **帕累托曲线分析**: 生成成本-性能权衡可视化
- **交互式仪表板**: 基于 Streamlit 的 Web 界面，支持配置对比、成本分析等

#### Key Implementation Features
- **多维配置空间**: 支持模型、硬件、调度器、并行策略等维度的组合优化
- **SLO 驱动优化**: 基于 TTFT、TBT、调度延迟等服务级别目标进行搜索
- **并行化执行**: CPU 亲和性绑定和缓存预热机制
- **可视化分析**: 端到端的配置优化到性能分析流程

---

### Trace Files Analysis (2025-07-28)

**User Request**: 查看 `data/processed_traces` 目录文件内容和作用

**Key Findings**:

#### Available Trace Files
1. **mooncake_conversation_trace.csv**: 
   - 包含前缀缓存信息 (`block_hash_ids`, `session_id`)
   - 适合测试前缀缓存策略效果
   - 用于模拟对话型工作负载

2. **splitwise_conv.csv**: 
   - 简化格式，专注基本token长度分布
   - 模拟对话场景基础工作负载

3. **arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv**: 
   - 包含预填充/解码比率数据
   - 基于LLaMA2分词器统计
   - 适合文档摘要任务模拟

4. **splitwise_code.csv**: 
   - 专门针对代码生成场景
   - 模拟编程辅助工作负载

#### Data Characteristics
- **Token长度分布**: 对话(2K-8K输入)、摘要(2K-4K输入)、代码(变化范围大)
- **时间特征**: `arrived_at`字段模拟真实请求到达模式
- **应用场景**: 性能基准测试、调度算法验证、容量规划、前缀缓存优化

#### Usage in Vidur
这些trace文件为系统提供真实工作负载数据基础，通过配置文件引用：
```yaml
traces:
  - name: splitwise_conv
    trace_file: "./data/processed_traces/splitwise_conv.csv"
    max_seq_len: 16384
```

---

### Config Explorer Parameters Analysis (2025-07-28)

**User Request**: 查看 `docs/config_explorer.md` 内容，总结参数含义和配置寻优类型及方式

**Key Findings**:

#### 核心配置参数

**集群配置 (clusters)**:
- `device`: GPU型号 (h100, a100等)
- `network_device`: 网络设备类型 (h100_dgx, a100_dgx等)
- `gpus_per_node`: 每节点GPU数量

**全局调度器 (global_schedulers)** - 副本间请求路由策略:
- `round_robin`: 轮询调度
- `sticky_lop`: 粘性最少未完成预填充调度
- `lop_uncached`: 无缓存的最少未完成预填充
- `tolerant_sticky_lop_uncached`: 容错粘性调度 (需要`tolerance_factor`参数)
- `ranked_sticky_lop_uncached`: 排名粘性调度 (需要`top_k`参数)

**副本调度器 (replica_schedulers)**:
- `vllm_v1`: VLLM V1调度器
- `chunk_size`: 块大小配置 (512, 1024等)

**SLO配置 (slo_configs)** - 服务级别目标:
- `type`: 多条件逻辑关系 (`and`/`or`)
- `quantile`: 百分位数 (如0.99表示P99)
- `metric`: 性能指标类型
  - `prefill_e2e_time`: 预填充端到端时间
  - `request_e2e_time`: 请求端到端时间  
  - `decode_time_execution_plus_preemption_normalized`: 标准化解码时间
- `value`: SLO阈值

**工作负载配置 (traces)**:
- `trace_file`: 数据文件路径
- `max_seq_len`: 最大序列长度
- `enable_prefix_caching`: 是否启用前缀缓存

**系统参数**:
- `batch_sizes`: 批大小选项
- `tp_dimensions`: 张量并行维度
- `pp_dimensions`: 流水线并行维度
- `exclude_tp_dims`: 模型排除的张量并行维度

#### 配置寻优类型

**1. QPS搜索** (`search_for: qps`):
- 目标: 在给定副本数下找到满足SLO的最大QPS
- 参数: `num_replicas`(固定), `qps`(起始点), `num_requests`(测试量)

**2. 副本数搜索** (`search_for: num_replicas`):
- 目标: 在给定QPS下找到满足SLO的最少副本数
- 参数: `qps`(固定), `num_replicas`(起始点)

**3. 等值QPS搜索** (`search_for: isoqps`):
- 目标: 在不同QPS倍数下进行性能测试
- 参数: `qps_multipliers`(倍数列表)

#### 寻优算法与执行方式

**二分搜索算法**:
- 自动找到满足SLO约束的最优配置点
- 搜索精度: `--min-search-granularity` (默认1%)
- 最大迭代: `--max-iterations` (默认10次)

**并行化执行**:
- 基于Ray框架的分布式并行搜索
- 每CPU核心运行独立配置搜索
- 支持CPU亲和性绑定优化性能

**运行命令**:
```bash
python -u -m vidur.config_optimizer.config_explorer.main \
--config-path path/to/config.yml \
--cache-dir cache \
--output-dir config_optimizer_output \
--time-limit 180 \
--num-threads <cores> \
--min-search-granularity 1.0 \
--max-iterations 10
```

**结果组织**:
```
config_optimizer_output/
├── runs/<hash>/r<replicas>_q<qps>/<timestamp>/
│   ├── plots/ config.json request_metrics.csv
│   └── output.log
└── args.json config.json
```

#### 实现特点
- **笛卡尔积组合**: 自动组合所有配置维度进行全面搜索
- **SLO驱动**: 基于服务级别目标约束进行优化
- **可恢复搜索**: 支持从中断点恢复搜索
- **性能优化**: 推荐使用tmpfs加速I/O操作

## Memorized Interactions

- Memorized the config optimizer analysis details from the recent user request about exploring the `vidur/config_optimizer` directory structure and functionality
- Memorized the `test` memory