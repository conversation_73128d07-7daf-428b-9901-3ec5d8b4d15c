name: "Run linter"
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
permissions:
  contents: read
  packages: write
defaults:
  run:
    shell: bash -l {0}
jobs:
  sanity_check:
    runs-on: "ubuntu-latest"
    steps:
      - name: "Checkout Repository"
        uses: actions/checkout@v3
      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          # Install a specific version of uv.
          version: "0.7.3"
      - name: Install the project
        run: uv sync --locked --all-extras --dev
      - name: Run black
        run: uv run black vidur
      - name: Run isort
        run: uv run isort --profile black vidur
